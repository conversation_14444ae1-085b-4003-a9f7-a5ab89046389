<?php

use App\Exports\SanexAuditExport;
use App\Http\Controllers\AboutController;
use App\Http\Controllers\Admin\DriverController;
use App\Http\Controllers\Admin\RoomController;
use App\Http\Controllers\Admin\ServiceController;
use App\Http\Controllers\Admin\TrackingUpdateController;
use App\Http\Controllers\AuditController;
use App\Http\Controllers\Admin\QueueStatusController;
use App\Http\Controllers\DescriptionController;
use App\Http\Controllers\Auth\ConfirmPasswordController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Controllers\Auth\VerificationController;
use App\Http\Controllers\Billing\WebBillingIpayController;
use App\Http\Controllers\Billing\WebBillingTbcPayController;
use App\Http\Controllers\BranchBoxController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\CashflowController;
use App\Http\Controllers\CitiesController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\ExcelController;
use App\Http\Controllers\FlightsController;
use App\Http\Controllers\GoodsController;
use App\Http\Controllers\HelperController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\IpayController;
use App\Http\Controllers\ItemCategoryController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\NotificationDispatcherController;
use App\Http\Controllers\ParametersController;
use App\Http\Controllers\ParcelCoordinateController;
use App\Http\Controllers\PosterController;
use App\Http\Controllers\ProhibitedItemsController;
use App\Http\Controllers\PromotionController;
use App\Http\Controllers\SMScontroller;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\TbcPayController;
use App\Http\Controllers\TermsController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\UserController;
use App\Models\Branch;
use App\Models\User;
use App\Notifications\sentgoods;
use App\Support\SMS;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Maatwebsite\Excel\Facades\Excel;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;


//ფუნქცია რომელიც გვეხმარება როცა გაიჭედება ამანათების ჩამოყვანა ფილიალის მიხედვით, შეგვიძლია ჩამოვიყვანოთ ისინი რომელიც დარჩება გამოგზავნილში
//Route::get('/chamoyvana', function (){
//    $service = new \App\Service\GoodService;
//    $count = $service->deliver();
//    dd('success', "count:$count");
//});

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
//TbcPay::routes(
//    TbcPayController, // Default: 'TbcPayController'
//    success, // Default: 'success'
//    fail // Default: 'fail'
//);

Route::get('police-export', function (){

    return Excel::download(new \App\Exports\TookOutReport(\request('start'), request('end')), 'users.xlsx');
});

Route::group(['prefix' => LaravelLocalization::setLocale()], function () {

    Route::post('require-call', [\App\Http\Controllers\ContactController::class, 'require'])->name('contact.require');
//
    Route::get('/cashflow/invoice/{invoice}', [\App\Http\Controllers\CashflowController::class, 'invoice'])->name('cashflow.public.invoice');

    Route::get('/notification', function () {
//    $invoice = Invoice::find(1);
        $goods = \App\Models\goods::first();
        return (new sentgoods($goods))
            ->toMail('test');
    });

//Route::get('dato', function () {
//    Mail::to("<EMAIL>")->send(new RegistrationMail());
//});

    Route::get('datosms', function () {
        $smsText = "თქვენი ამანათები დაექვემდებარა განბაჟებას. გთხოვთ ნივთის შესყიდვის ინვოის(ები)ი მოგვაწოდოთ მეილზე <EMAIL>.
დანიშნულებაში მიუთითეთ თქვენი სახელი და გვარი. დეკლარაციის დაბეჭდვის შემდეგ შესაძლებელი იქნება ნივთის ოფისიდან გატანა.";
        (new SMS("598789722", $smsText))->send();
    });
    Route::get('/', [HomeController::class, 'index'])->name('user.home');
    Route::get('/rules', [HomeController::class, 'rules'])->name('user.rules');
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login.Form');
    Route::post('/login', [LoginController::class, 'login'])->name('login');
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');
    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('registerForm');
    Route::post('/register', [RegisterController::class, 'register'])->name('register');
    Route::get('/password/confirm', [ConfirmPasswordController::class, 'showConfirmForm'])->name('password.confirm');
    Route::post('/password/confirm', [ConfirmPasswordController::class, 'confirm']);
    Route::get('/password/reset', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
    Route::post('/password/email', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');
    Route::get('/password/reset/{token}', [ResetPasswordController::class, 'showResetForm'])->name('password.reset');
    Route::post('/password/reset', [ResetPasswordController::class, 'reset'])->name('password.update');
    Route::get('email/verify', [VerificationController::class, 'show'])->name('verification.notice');
    Route::get('email/verify/{id}/{hash}', [VerificationController::class, 'verify'])->name('verification.verify');
    Route::post('email/resend', [VerificationController::class, 'resend'])->name('verification.resend');

    Route::get('/register2', [HomeController::class, 'register2'])->name('user.register2');
    Route::get('/faq', [HomeController::class, 'faq'])->name('user.faq');
    Route::get('/about', [HomeController::class, 'about'])->name('user.about');
    Route::get('/contact', function () {
        $branches = Branch::all();
        return view('user.contact', compact('branches'));
    })->name("user.contact");


    Route::get('/customer3', function () {
        return view('layouts.customer3');
    })->name("layouts.customer3");

    Auth::routes();

    Route::group(['middleware' => ['role:super|manager', 'log.admin.activity'], 'prefix' => 'admin'], function () {
        Route::prefix('/promotion')->name('promotion.')->group(function () {
            Route::get('/', [PromotionController::class, 'index'])->name('index')->middleware('permission:PromotionController.index');
            Route::get('create', [PromotionController::class, 'createForm'])->name('create.form')->middleware('permission:PromotionController.create');
            Route::post('create', [PromotionController::class, 'create'])->name('create')->middleware('permission:PromotionController.create');
            Route::get('show/{id}', [PromotionController::class, 'show'])->name('show')->middleware('permission:PromotionController.edit');
            Route::post('edit/{id}', [PromotionController::class, 'edit'])->name('edit')->middleware('permission:PromotionController.edit');
            Route::get('remove/{id}', [PromotionController::class, 'remove'])->name('remove')->middleware('permission:PromotionController.remove');
        });


        Route::prefix('/poster')->name('poster.')->group(function () {
            Route::get('/', [PosterController::class, 'index'])->name('index')->middleware('permission:PosterController.index');

            Route::get('create', [PosterController::class, 'createForm'])->name('create.form')->middleware('permission:PosterController.create');
            Route::post('create', [PosterController::class, 'create'])->name('create')->middleware('permission:PosterController.create');

            Route::get('show/{id}', [PosterController::class, 'show'])->name('show')->middleware('permission:PosterController.edit');
            Route::post('edit/{id}', [PosterController::class, 'edit'])->name('edit')->middleware('permission:PosterController.edit');

            Route::get('remove/{id}', [PosterController::class, 'remove'])->name('remove')->middleware('permission:PosterController.remove');

            Route::prefix('/job')->name('job.')->group(function () {
                Route::get('/', [PosterController::class, 'indexJob'])->name('index')->middleware('permission:PosterController.indexJob');
                Route::get('create', [PosterController::class, 'createFormJob'])->name('create.form')->middleware('permission:PosterController.createJob');
                Route::post('create', [PosterController::class, 'createJob'])->name('create')->middleware('permission:PosterController.createJob');

                Route::get('show/{id}', [PosterController::class, 'showJob'])->name('show')->middleware('permission:PosterController.editJob');
                Route::post('edit/{id}', [PosterController::class, 'editJob'])->name('edit')->middleware('permission:PosterController.editJob');

                Route::get('remove/{id}', [PosterController::class, 'removeJob'])->name('remove')->middleware('permission:PosterController.removeJob');
            });

        });

        Route::prefix('/contact')->group(function () {
            Route::get('/', [ContactController::class, 'index'])->middleware('permission:ContactController.index')->name('contact.index');
            Route::post('/list', [ContactController::class, 'list'])->middleware('permission:ContactController.index')->name('contact.list');
            Route::get('/update-status/{id}', [ContactController::class, 'updateStatus'])->middleware('permission:ContactController.index')->name('contact.update.status');
        });

        Route::prefix('/task')->group(function () {
            Route::view('/', 'admin.task.index')->middleware('permission:TaskController.index')->name('task.index');
            Route::post('/data', [TaskController::class, 'data'])->middleware('permission:TaskController.index')->name('task.data');
            Route::get('/create', [TaskController::class, 'create'])->middleware('permission:TaskController.create')->name('task.create');
            Route::post('/create', [TaskController::class, 'createPost'])->middleware('permission:TaskController.create')->name('task.create.post');
            Route::get('/update-status/{id}', [TaskController::class, 'updateStatus'])->middleware('permission:TaskController.index')->name('task.update.status');
            Route::get('/update/{id}', [TaskController::class, 'update'])->middleware('permission:TaskController.update')->name('task.update');
            Route::post('/update/{id}', [TaskController::class, 'updatePost'])->middleware('permission:TaskController.update')->name('task.update.post');
            Route::get('/delete/{id}', [TaskController::class, 'delete'])->middleware('permission:TaskController.delete')->name('task.delete');
        });


        Route::prefix('/branch-boxes')->name('branch.boxes.')->group(function () {
            Route::prefix('/box')->name('box.')->group(function () {
                Route::post('/create', [BranchBoxController::class, 'boxCreate'])->middleware('permission:BranchBoxController.boxCreate')->name('create');
                Route::get('/create', [BranchBoxController::class, 'create'])->middleware('permission:BranchBoxController.create')->name('createForm');
                Route::get('/create-section', [BranchBoxController::class, 'createSection'])->middleware('permission:BranchBoxController.createSection')->name('createSection');
                Route::get('/list', [BranchBoxController::class, 'list'])->middleware('permission:BranchBoxController.list')->name('list');
                Route::get('/list-section', [BranchBoxController::class, 'listSection'])->middleware('permission:BranchBoxController.listSection')->name('listSection');

                Route::get('qr-download/{id}', [BranchBoxController::class, 'sectionQr'])->middleware('permission:BranchBoxController.sectionQr')->name('qr.download');

                Route::get('/show/{id}', [BranchBoxController::class, 'show'])->middleware('permission:BranchBoxController.show')->name('show');
                Route::get('/show-section/{id}', [BranchBoxController::class, 'showSection'])->middleware('permission:BranchBoxController.showSection')->name('showSection');
                Route::post('/edit/{id}', [BranchBoxController::class, 'edit'])->middleware('permission:BranchBoxController.edit')->name('edit');
                Route::get('/remove/{id}', [BranchBoxController::class, 'remove'])->middleware('permission:BranchBoxController.remove')->name('remove');

                Route::prefix('/delivery')->name('delivery.')->group(function () {
                    Route::post('/boxFill', [BranchBoxController::class, 'boxFill'])->middleware('permission:BranchBoxController.boxFill')->name('checkTrack'); // check trackcode in another box, ყუთებში გადანაწილება
                    Route::post('/addContainer', [BranchBoxController::class, 'addContainer'])->middleware('permission:BranchBoxController.addContainer')->name('addContainer'); // შექმნის ფეიჯზე დამატებული ამანათის წაშლა
                    Route::delete('/deleteContainer/{id}', [BranchBoxController::class, 'deleteContainer'])->middleware('permission:BranchBoxController.deleteContainer')->name('deleteContainer'); // შექმნის ფეიჯზე დამატებული ამანათის წაშლა
                    Route::get('/create', [BranchBoxController::class, 'deliveryCreate'])->middleware('permission:BranchBoxController.deliveryCreate')->name('create'); //გადანაწილებული ანუ შექმნილი ყუთის ნახვა, აქვე ვხედავ სტატუსსაც, ტრანსპორტირების
                    Route::get('/list', [BranchBoxController::class, 'deliveryList'])->middleware('permission:BranchBoxController.deliveryList')->name('list'); //გადანაწილებული ანუ შექმნილი ყუთის ნახვა, აქვე ვხედავ სტატუსსაც, ტრანსპორტირების
                    Route::get('/show/{id}', [BranchBoxController::class, 'showDelivery'])->middleware('permission:BranchBoxController.showDelivery')->name('show'); //გადანაწილებული ანუ შექმნილი ყუთის ნახვა, აქვე ვხედავ სტატუსსაც, ტრანსპორტირების
                    Route::post('/edit', [BranchBoxController::class, 'editDelivery'])->middleware('permission:BranchBoxController.editDelivery')->name('edit'); //გადანაწილებული ყუთის რედაქტირება, სტატუსის ცვლილება მხოლოდ, ამოშლა ცალკე ენდფოინთზეა, თუ ჩამატება მინდა boxFill ფუნქციას ვიყენებ
                    Route::get('/remove/{id}', [BranchBoxController::class, 'deleteDelivery'])->middleware('permission:BranchBoxController.deleteDelivery')->name('remove'); //გადანაწილებული ყუთის წაშლა
                    Route::delete('/container/remove/{goodId}', [BranchBoxController::class, 'deleteBoxContainer'])->middleware('permission:BranchBoxController.deleteBoxContainer')->name('container.remove'); // გადანაწილებული ყუთიდან ამანათის ამოშლა
                    Route::get('/flightContainer', [BranchBoxController::class, 'flightContainer'])->middleware('permission:BranchBoxController.flightContainer')->name('flight.container'); //კონტროლდება transform პარამეტრით, თუ არის 1, მოაქვს ის ამანათები რომლებიც არ არის გადანაწილებული, თუ უდრის 0 მოაქვს ის ამანათები რომლებიც გადანაწილებულია
                    Route::get('/flightDelivery/{id}', [BranchBoxController::class, 'flightDelivery'])->middleware('permission:BranchBoxController.flightDelivery')->name('flight.delivery');
                });
            });
            Route::prefix('/local-distribution')->name('local.distribution.')->group(function () {
                Route::get('/box-by-name', [BranchBoxController::class, 'boxByName'])->middleware('permission:BranchBoxController.localDistributionShow')->name('boxByName');
                Route::get('/boxes/{id}', [BranchBoxController::class, 'boxes'])->middleware('permission:BranchBoxController.boxes')->name('boxes'); //ამანათების გადანაწილება ფილიალებისთვის
                Route::get('/create', [BranchBoxController::class, 'localDistributionForm'])->middleware('permission:BranchBoxController.localDistributionForm')->name('create.form'); //ამანათების გადანაწილება ფილიალებისთვის
                Route::post('/create', [BranchBoxController::class, 'localDistributionCreate'])->middleware('permission:BranchBoxController.localDistributionCreate')->name('create'); //ამანათების გადანაწილება ფილიალებისთვის
                Route::get('/list', [BranchBoxController::class, 'localDistributionList'])->middleware('permission:BranchBoxController.localDistributionList')->name('list'); //ვხედავთ ყველა ყუთს ფილიალის მიხედვით, შემდეგ ვირჩევთ და ვნახულობთ შიგნით რა გვაქვს
                Route::get('/show/{id}', [BranchBoxController::class, 'localDistributionShow'])->middleware('permission:BranchBoxController.localDistributionShow')->name('show');//გადანაწილებული ამანათების ნახვა, არ მოაქვს ის ამანათები რომლებიც გაცემულია
                Route::post('/edit', [BranchBoxController::class, 'localDistributionEdit'])->middleware('permission:BranchBoxController.localDistributionEdit')->name('edit'); //ვარედაქტირებთ ბოქს აიდს და ამანათის აიდს
                Route::delete('/remove/{id}', [BranchBoxController::class, 'localDistributionRemove'])->middleware('permission:BranchBoxController.localDistributionRemove')->name('remove'); //ბოქსიდან გადანაწილებული ამანათის ამოშლა
            });
        });

        Route::prefix('description')->group(function (){
            Route::get('index', [DescriptionController::class, 'index'])->name('description.index');
            Route::get('create', [DescriptionController::class, 'create'])->name('description.create');
            Route::post('store', [DescriptionController::class, 'store'])->name('description.store');
            Route::get('edit/{id}', [DescriptionController::class, 'edit'])->name('description.edit');
            Route::post('update/{id}', [DescriptionController::class, 'update'])->name('description.update');
            Route::get('destroy/{id}', [DescriptionController::class, 'destroy'])->name('description.destroy');
            Route::post('scan-parcel', [DescriptionController::class, 'scanParcel'])->name('description.scan-parcel');
            Route::post('get-branch-parcels', [DescriptionController::class, 'getBranchParcels'])->name('description.get-branch-parcels');
        });

        //test routes

        Route::get('/testsms', [\App\Http\Controllers\TestController::class, 'sms'])->name('testsms');
        //end

        Route::get('/delete-duplicate-invoices/{flightId}/{branchId}', [HelperController::class, 'detectDuplicateGoodsInvoices'])->middleware('permission:HelperController.detectDuplicateGoodsInvoices'); //delete duplicate invoices

//        daabrunos mtliani livewire componenti
        Route::post('/logout', [HomeController::class, 'logout'])->name('admin.logout');
        Route::get('/export/{flight?}', [ExcelController::class, 'export'])->middleware('permission:ExcelController.export')->name('excel.export');
//        Route::get('/flights', 'FlightsController@index')->name('flights.index');
        Route::controller(FlightsController::class)
            ->group(function () {
                Route::get('/flights/search', 'flightSearch')->middleware('permission:FlightsController.index2')->name('flights.search');
                Route::post('/flights/paginate', 'paginate')->middleware('permission:FlightsController.paginate')->name('flights.paginate');
                Route::get('/flights2', 'index2')->middleware('permission:FlightsController.index2')->name('flights.index2');
                Route::post('/flights', "store")->middleware('permission:FlightsController.store')->name("flights.store");
                Route::post('/flights/excel', "excel")->middleware('permission:FlightsController.excel')->name("flights.excel");
                Route::post('/flights/excel2', "excel2")->middleware('permission:FlightsController.excel2')->name("flights.excel2");
                Route::get('/flights/create/', 'create')->middleware('permission:FlightsController.create')->name('flights.create');
                Route::get('/flights/edit/{flight}', 'edit')->middleware('permission:FlightsController.edit')->name('flights.edit');
                Route::post('/flights/update/{flight}', 'update')->middleware('permission:FlightsController.update')->name('flights.update');
                Route::get('/flights/delete/{flight}', 'delete')->middleware('permission:FlightsController.delete')->name('flights.delete');
                Route::post('/flights/delete/', 'delete2')->middleware('permission:FlightsController.delete2')->name('flights.delete2');
                Route::get('/canceledGoods/', 'canceledGoods')->middleware('permission:FlightsController.canceledGoods')->name('canceled.goods');
            });
        Route::controller(RoomController::class)
            ->name('room.')
            ->group(function () {
                Route::get('/payment/log', 'paymentLog')->middleware('permission:RoomController.paymentLog')->name("payment.log");
                Route::post('/payment/log', 'paymentLogData')->middleware('permission:RoomController.paymentLogData')->name("payment.log.data");
            });

        Route::prefix('notification-dispatcher')->group(function (){
            Route::get('index', [NotificationDispatcherController::class, 'index'])->name('notification.dispatcher.index');
            Route::get('create', [NotificationDispatcherController::class, 'create'])->name('notification.dispatcher.create');
            Route::post('send', [NotificationDispatcherController::class, 'send'])->name('notification.dispatcher.send');
            Route::post('/load-template',[NotificationDispatcherController::class, 'loadTemplate'])->name('notification.dispatcher.loadTemplate');
        });
        Route::prefix('user-notifications')->group(function (){
            Route::get('index', [NotificationController::class, 'index'])->name('user.message.index');
            Route::get('create', [NotificationController::class, 'create'])->name('user.message.create');
            Route::post('send', [NotificationController::class, 'send'])->name('user.message.send');
            Route::get('/users', [NotificationController::class, 'users'])->name('user.message.users');

        });

        Route::controller(GoodsController::class)
            ->group(function () {
                Route::get('took-out-goods', 'tookOutGoods')->name('tookOutGoods.log')->middleware('permission:GoodsController.tookOutGoods');
                Route::get('/tookout-log-data', 'tookOutGoodsData')->middleware('permission:GoodsController.tookOutGoodsData')->name('tookout.log.data');

                Route::get('branch-log', 'branchLog')->middleware('permission:GoodsController.branchLog')->name('branch.log');
                Route::get('branch-log-date', 'getBranchLogData')->middleware('permission:GoodsController.getBranchLogData')->name('branch.log.data');

                Route::get('deleted-goods', 'deletedGoods')->middleware('permission:GoodsController.deletedGoods')->name('deleted.goods');
                Route::get('deleted-goods-data', 'deletedGoodsData')->middleware('permission:GoodsController.deletedGoodsData')->name('deleted.goods.data');

                Route::get('recover-deleted-good/{id}', 'recoverDeletedGood')->name('recover.deleted.goods')->middleware('permission:GoodsController.recoverDeletedGood');
                Route::post('export', 'export')->middleware('permission:GoodsController.Goods_index')->name('goods.export');
                Route::get('/tracking/{tracking_code}', 'parcelScan')->middleware('permission:GoodsController.Goods_index')->name('goods.index3.parcel.scan');
                Route::get('index3', 'index3')->middleware('permission:GoodsController.Goods_index')->name('goods.index3');
                Route::get('index3-data', 'index3Data')->middleware('permission:GoodsController.Goods_index')->name('goods.index3.data');
                Route::get('/', 'welcome')->name('dashboard.welcome');

                Route::get('/statistics', 'dashboard')->middleware('permission:GoodsController.statistics')->name('dashboard.index');
                Route::post('/parcels/paginate', 'paginate')->middleware('permission:GoodsController.paginate')->name('parcels.paginate');
                Route::post('/parcels/paginate2', 'paginate2')->middleware('permission:GoodsController.paginate2')->name('parcels.paginate2');
                Route::post('/courier/paginate', 'paginate_courier')->middleware('permission:GoodsController.paginate_courier')->name('courier.paginate');
                Route::post('/parcels', 'store')->middleware('permission:GoodsController.store')->name('parcels.store');
                //livewire table
                Route::get('/goods', 'Goods_index')->middleware('permission:GoodsController.Goods_index')->name('goods.index2');
                //old table
                Route::get('/parcels', 'index')->middleware('permission:GoodsController.index')->name('goods.index');
                Route::post('/parcels', "store")->middleware('permission:GoodsController.store')->name("parcels.store");
                Route::get('/parcels/create', 'create')->middleware('permission:GoodsController.create')->name('parcels.create');
                Route::get('/parcels/edit/{goods}', 'edit')->middleware('permission:GoodsController.edit')->name('parcels.edit');
                Route::post('/parcels/update/{goods}', 'update')->middleware('permission:GoodsController.update')->name('parcels.update');
                Route::get('/parcels/delete/{goods}', 'delete')->middleware('permission:GoodsController.delete')->name('parcels.delete');
                Route::post('/parcels/find', 'findGoodById')->middleware('permission:GoodsController.findGoodById')->name('parcels.find');
                Route::get('/parcels/action', 'action')->middleware('permission:GoodsController.action')->name('live_search.action');
                Route::post('/parcels/delete/', 'delete2')->middleware('permission:GoodsController.delete2')->name('parcels.delete2');

                Route::post('/parcels/clearanceChange/', 'clearanceChange')->middleware('permission:GoodsController.clearanceChange')->name('parcels.clearanceChange');
                Route::post('/parcels/bulkUpdateStatus/', 'bulkUpdateStatus')->middleware('permission:GoodsController.bulkUpdateStatus')->name('parcels.bulkUpdateStatus');

                Route::get('/declaration', 'declaration')->middleware('permission:GoodsController.declaration')->name('declaration.index');
                Route::post('/declaration_update', 'declaration_update')->middleware('permission:GoodsController.declaration_update')->name('declaration.update');
                Route::post('/declaration_update3', 'declaration_update3')->middleware('permission:GoodsController.declaration_update')->name('declaration.update3');
                Route::get('/courier', 'courier')->middleware('permission:GoodsController.courier')->name('courier.index');
                Route::get('/courier2', 'courier2')->middleware('permission:GoodsController.courier2')->name('courier.index2');
            });
        Route::controller(CitiesController::class)
            ->group(function () {
                Route::get('/cities', 'index')->middleware('permission:CitiesController.index')->name('cities.index');
                Route::post('/cities', "store")->middleware('permission:CitiesController.store')->name("cities.store");
                Route::get('/cities/create', 'create')->middleware('permission:CitiesController.create')->name('cities.create');
                Route::get('/cities/edit/{cities}', 'edit')->middleware('permission:CitiesController.edit')->name('cities.edit');
                Route::post('/cities/update/{cities}', 'update')->middleware('permission:CitiesController.update')->name('cities.update');
                Route::get('/cities/delete/{cities}', 'delete')->middleware('permission:CitiesController.delete')->name('cities.delete');
                Route::post('/cities/delete/', 'delete2')->middleware('permission:CitiesController.delete2')->name('cities.delete2');
                Route::post('/cities/paginate', "paginate")->middleware('permission:CitiesController.paginate')->name("cities.paginate");
            });
        Route::controller(ItemCategoryController::class)
            ->group(function () {
                Route::get('/item_category', 'index')->middleware('permission:ItemCategoryController.index')->name('item_category.index');
                Route::post('/item_category', 'store')->middleware('permission:ItemCategoryController.store')->name("item_category.store");
                Route::get('/item_category/create', 'create')->middleware('permission:ItemCategoryController.create')->name('item_category.create');
                Route::get('/item_category/edit/{item_category}', 'edit')->middleware('permission:ItemCategoryController.edit')->name('item_category.edit');
                Route::post('/item_category/update/{item_category}', 'update')->middleware('permission:ItemCategoryController.update')->name('item_category.update');
                Route::get('/item_category/delete/{item_category}', 'delete')->middleware('permission:ItemCategoryController.delete')->name('item_category.delete');
                Route::post('/item_category/delete/', 'delete2')->middleware('permission:ItemCategoryController.delete2')->name('item_category.delete2');
                Route::post('/item_category/paginate', 'paginate')->middleware('permission:ItemCategoryController.paginate')->name("item_category.paginate");
            });
        Route::controller(ProhibitedItemsController::class)
            ->group(function () {
                Route::get('/prohibited', 'index')->middleware('permission:ProhibitedItemsController.index')->name('prohibited.index');
                Route::post('/prohibited', "store")->middleware('permission:ProhibitedItemsController.store')->name("prohibited.store");
                Route::get('/prohibited/create', 'create')->middleware('permission:ProhibitedItemsController.create')->name('prohibited.create');
                Route::get('/prohibited/edit/{prohibited_items}', 'edit')->middleware('permission:ProhibitedItemsController.edit')->name('prohibited.edit');
                Route::post('/prohibited/update/{prohibited_items}', 'update')->middleware('permission:ProhibitedItemsController.update')->name('prohibited.update');
                Route::get('/prohibited/delete/{prohibited_items}', 'delete')->middleware('permission:ProhibitedItemsController.delete')->name('prohibited.delete');
                Route::post('/prohibited/delete/', 'delete2')->middleware('permission:ProhibitedItemsController.delete2')->name('prohibited.delete2');
                Route::post('/prohibited/paginate', "paginate")->middleware('permission:ProhibitedItemsController.paginate')->name("prohibited.paginate");
            });
        Route::controller(BranchController::class)
            ->group(function () {
                Route::get('/branch', 'index')->middleware('permission:BranchController.index')->name('branch.index');
                Route::post('/branch', 'store')->middleware('permission:BranchController.store')->name("branch.store");
                Route::get('/branch/create', 'create')->middleware('permission:BranchController.create')->name('branch.create');
                Route::get('/branch/edit/{branch}', 'edit')->middleware('permission:BranchController.edit')->name('branch.edit');
                Route::post('/branch/update/{branch}', 'update')->middleware('permission:BranchController.update')->name('branch.update');
                Route::get('/branch/delete/{branch}', 'delete')->middleware('permission:BranchController.delete')->name('branch.delete');
                Route::post('/branch/paginate', 'paginate')->middleware('permission:BranchController.paginate')->name("branch.paginate");
                Route::post('/branch/delete/', 'delete2')->middleware('permission:BranchController.delete2')->name('branch.delete2');
            });
        Route::controller(ParcelCoordinateController::class)
            ->group(function () {
                Route::get('/address', 'index')->middleware('permission:ParcelCoordinateController.index')->name('address.index');
                Route::post('/address', "store")->middleware('permission:ParcelCoordinateController.store')->name("address.store");
                Route::get('/address/create', 'create')->middleware('permission:ParcelCoordinateController.create')->name('address.create');
                Route::get('/address/edit/{parcel_coordinates}', 'edit')->middleware('permission:ParcelCoordinateController.edit')->name('address.edit');
                Route::post('/address/update/{parcel_coordinates}', 'update')->middleware('permission:ParcelCoordinateController.update')->name('address.update');
                Route::post('/address/paginate', "paginate")->middleware('permission:ParcelCoordinateController.paginate')->name("address.paginate");
                Route::get('/address/delete/{parcel_coordinates}', 'delete')->middleware('permission:ParcelCoordinateController.delete')->name('address.delete');
                Route::post('/address/delete/', 'delete2')->middleware('permission:ParcelCoordinateController.delete2')->name('address.delete2');
            });
        Route::controller(CashflowController::class)
            ->group(function () {
                Route::get('/cashflow', 'index')->middleware('permission:CashflowController.index')->name('cashflow.index');
                Route::post('/cashflow', "store")->middleware('permission:CashflowController.store')->name("cashflow.store");
                Route::get('/cashflow/create/', 'create')->middleware('permission:CashflowController.create')->name('cashflow.create');
                Route::get('/cashflow/edit/{cashflow}', 'edit')->middleware('permission:CashflowController.edit')->name('cashflow.edit');
                Route::post('/cashflow/update/{cashflow}', 'update')->middleware('permission:CashflowController.update')->name('cashflow.update');
                Route::get('/cashflow/delete/{cashflow}', 'delete')->middleware('permission:CashflowController.delete')->name('cashflow.delete');
                Route::post('/cashflow/delete/', 'delete2')->middleware('permission:CashflowController.delete2')->name('cashflow.delete2');
                Route::post('/cashflow/paginate', "paginate")->middleware('permission:CashflowController.paginate')->name("cashflow.paginate");
                Route::post('/cashflow/paginate2', "paginate2")->middleware('permission:CashflowController.paginate2')->name("cashflow.paginate2");
                Route::get('/cashflow/invoice/{invoice}', 'invoice')->middleware('permission:CashflowController.invoice')->name('cashflow.invoice');
                Route::get('/cashflow/export-form', 'exportForm')->name('cashflow.export.form');
                Route::post('/cashflow/export', 'export')->name('cashflow.export');
            });
        Route::get('/users/search', function (Request $request) {
            $search = $request->get('search');
            $normalizedSearch = preg_replace('/\s+/', ' ', trim($search)); // გაასუფთავოს ზედმეტი space-ები

            $count = (int) (max(1, strlen($search)) * 10);
            $users = User::query()
                ->where(function ($query) use ($normalizedSearch) {
                    // გაერთიანებული ველი: first_name + last_name + user_room_code
                    $query->where(\DB::raw("CONCAT_WS(' ', first_name_ge, last_name_ge, user_room_code)"), 'LIKE', "%{$normalizedSearch}%")
                        ->orWhere(\DB::raw("CONCAT_WS(' ', last_name_ge, first_name_ge, user_room_code)"), 'LIKE', "%{$normalizedSearch}%");
                })
                ->orWhere('user_room_code', 'LIKE', "%{$search}%")
                ->limit($count)
                ->get();


            return response()->json($users);
        })->name('user.search');
        Route::controller(UserController::class)
            ->group(function () {
                Route::get('user-export', 'export')->name('all.user.export');
                Route::get('user-paymentlog', 'paymentLogs')->name('user.payment.logs');
                Route::get('/user/report', 'report')->middleware('permission:UserController.report')->name('user.report');
                Route::post('/user/report', 'reportData')->middleware('permission:UserController.report')->name('user.report.data');
                Route::post('/user/sendSms', 'sendReportSms')->middleware('permission:UserController.report')->name('user.report.send.sms');
                Route::get('/user', 'index')->middleware('permission:UserController.index')->name('user.index');
                Route::get('/user2', 'index2')->middleware('permission:UserController.index2')->name('user.index2');
                Route::get('/user3', 'index3')->middleware('permission:UserController.index3')->name('user.index3');
                Route::post('/user', "store")->middleware('permission:UserController.store')->name("user.store");
                Route::get('/user/create/', 'create')->middleware('permission:UserController.create')->name('user.create');
                Route::get('/user/edit/{user}', 'edit')->middleware('permission:UserController.edit')->name('user.edit');
                Route::post('/user/update/{user}', 'update')->middleware('permission:UserController.update')->name('user.update');
                Route::get('/user/delete/{user}', 'delete')->middleware('permission:UserController.delete')->name('user.delete');
                Route::post('/user/delete/', 'delete2')->middleware('permission:UserController.delete2')->name('user.delete2');
                Route::post('/user/paginate', "paginate")->middleware('permission:UserController.paginate')->name("user.paginate");
                Route::post('/user/paginate2', "paginate2")->middleware('permission:UserController.paginate2')->name("user.paginate2");
            });
        Route::controller(ParametersController::class)
            ->group(function () {
                Route::get('/parameters', 'index')->middleware('permission:ParametersController.index')->name('parameters.index');
                Route::get('/parameters_price', 'price')->middleware('permission:ParametersController.price')->name('parameters.price');
                Route::get('/parameters/edit/{parameters}', 'edit')->middleware('permission:ParametersController.edit')->name('parameters.edit');
                Route::post('/parameters/update/{parameters}', 'update')->middleware('permission:ParametersController.update')->name('parameters.update');
            });
        Route::controller(TermsController::class)
            ->group(function () {
                Route::get('/terms', 'index')->middleware('permission:TermsController.index')->name('terms.index');
                Route::post('/terms', 'store')->middleware('permission:TermsController.store')->name("terms.store");
                Route::post('/terms/update', 'update')->middleware('permission:TermsController.update')->name('terms.update');
                Route::get('/cover', 'index2')->middleware('permission:TermsController.index2')->name('cover.index');
                Route::post('/cover', 'store2')->middleware('permission:TermsController.store2')->name("cove.store");
                Route::post('/cover/update/{cover}', 'update2')->middleware('permission:TermsController.update2')->name('cover.update');
            });
        Route::controller(SMScontroller::class)
            ->group(function () {
                Route::get('/sms_index', 'index')->middleware('permission:SMScontroller.index')->name('sms.index');
                Route::get('/sms_all_users', 'all_users')->middleware('permission:SMScontroller.all_users')->name('sms.all_users');
                Route::post('/sms_all_users_send', "sms_all_users_send")->middleware('permission:SMScontroller.sms_all_users_send')->name("sms.sms_all_users_send");
                Route::post('/sms_all_users_with', "sms_all_users_with")->middleware('permission:SMScontroller.sms_all_users_with')->name("sms.sms_all_users_with");
                Route::post('/sms/update/{sms}', 'update')->middleware('permission:SMScontroller.update')->name('sms.update');
                Route::get('/resume-sms/{id}', 'resumeSms')->middleware('permission:SMScontroller.sms_all_users_send')->name('sms.resume');
                //notification for Database
                Route::get('/db_all_users', 'db_all_users')->middleware('permission:SMScontroller.db_all_users')->name('sms.db_all_users');
                Route::post('/db_all_users_send', "db_all_users_send")->middleware('permission:SMScontroller.db_all_users_send')->name("sms.db_all_users_send");
                Route::post('destroy/{id}', 'destroy')->name('notification.destroy');
            });
        Route::controller(AboutController::class)
            ->group(function () {
                Route::get('/about', 'index')->middleware('permission:AboutController.index')->name('about.index');
                Route::post('/about', "store")->middleware('permission:AboutController.store')->name("about.store");
                Route::post('/about/update/{about}', 'update')->middleware('permission:AboutController.update')->name('about.update');
                Route::get('/emails', 'index')->middleware('permission:AboutController.index')->name('emails.index');
                Route::post('/emails', "store")->middleware('permission:AboutController.store')->name("emails.store");
                Route::post('/emails/update/{emails}', 'update')->middleware('permission:AboutController.update')->name('emails.update');
            });
        Route::controller(TransactionController::class)
            ->group(function () {
                Route::get('/transactions', 'index')->middleware('permission:TransactionController.index')->name('transactions.index');
                Route::get('/transactions_bog', 'index3')->middleware('permission:TransactionController.index3')->name('transactions.index3');
                Route::post('/transactions/paginate', "paginate")->middleware('permission:TransactionController.paginate')->name("transactions.paginate");
                Route::get('/transactions/refund/{transaction}', "refund")->middleware('permission:TransactionController.refund')->name("transactions.refund");
                //tbc pay chasaricxi aparati TBCPAYTransactionController
                Route::get('/tbs_transactions', 'index2')->middleware('permission:TransactionController.index2')->name('tbs_transactions.index');
                Route::post('/tbs_transactions/paginate', "paginate2")->middleware('permission:TransactionController.paginate2')->name("tbs_transactions.paginate");

            });
        //transactions
//        Route::get("/sms", function () {
//            (new SMS(995593363399, 'TEST SMS'))->send();
//        });
        //QR CODE SCANNER
        Route::get('/parcel/scan', [GoodsController::class, 'scanParcel'])->middleware('permission:GoodsController.scanParcel')->name('scanParcel.index');
//        Route::post('/parcel/{id}/scan', 'GoodsController@scanParcelPost')->name('scanParcelPost.index');


//            kalkulacia tanxis frenebze gadmotanili monacemebis.
//        Route::get('/calculate', function(){
//
////            $change_goods = Goods::where("flight_id",1)->get();
//            $change_goods = Goods::whereNotIn('flight_id', [263,264])->where("is_calculated",0)->limit(10000)->get();
//            $flightsellrate = 3.32;
//            $param = 9.90;
//
//
//            foreach ($change_goods as $change_good) {
//
//                if ($change_good->is_calculated == 0) {
//                    $change_good->flight_parcel_state = 'RECIEVED';
//                    $change_goods->sell_rate =  $param;
//                    $change_goods->goldex_kg_price = $flightsellrate;
//                    $change_good->price_to_pay = ($change_good->phisicalw * $change_goods->goldex_kg_price) * $change_goods->sell_rate;
//                    $change_good->is_calculated = 1;
//                    $change_good->save();
//                }
//            }
//        });

        Route::get('/room', [RoomController::class, 'index'])->middleware('permission:RoomController.index')->name('room.index');
        Route::get('/room/courier', [RoomController::class, 'indexCourier'])->middleware('permission:RoomController.index')->name('room.index.courier');

        // Tracking code update routes - only accessible by user with ID 8415
        Route::prefix('/tracking-update')->name('admin.tracking.update.')->middleware('specific.user:8415')->group(function () {
            Route::get('/', [TrackingUpdateController::class, 'index'])->name('index');
            Route::post('/process', [TrackingUpdateController::class, 'update'])->name('process');
            Route::get('/flights/search', [TrackingUpdateController::class, 'searchFlights'])->name('flights.search');
        });
        Route::post('/user/search', [RoomController::class, 'userSearch'])->middleware('permission:RoomController.userSearch')->name('user.search.ajax');
        Route::post('/order-took-out', [RoomController::class, 'orderTookOut'])->middleware('permission:RoomController.orderTookOut')->name('room.order.took.out');
        Route::post('/payment', [RoomController::class, 'payment'])->middleware('permission:RoomController.payment')->name('room.payment');

        // Audit routes
        Route::controller(AuditController::class)
            ->prefix('/audit')
            ->name('audit.')
            ->group(function () {
                Route::get('/', 'index')->name('index');
                Route::get('/export', 'export')->name('export');
                Route::get('/job-status/{jobId}', 'getJobStatus')->name('job-status');
            });

        // Queue Status routes
        Route::controller(QueueStatusController::class)
            ->prefix('/queue')
            ->name('admin.queue.')
            ->group(function () {
                Route::get('/status', 'index')->name('status');
                Route::post('/retry/{id}', 'retryJob')->name('retry');
                Route::delete('/clear-failed', 'clearFailedJobs')->name('clear-failed');
            });

        Route::resource('driver', DriverController::class)->middleware('permission:DriverController');

        // Admin Logs routes
        Route::prefix('/logs')->name('admin.logs.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\AdminLogController::class, 'index'])->name('index');
            Route::get('/show/{id}', [\App\Http\Controllers\Admin\AdminLogController::class, 'show'])->name('show');
            Route::get('/export', [\App\Http\Controllers\Admin\AdminLogController::class, 'export'])->name('export');
            Route::post('/cleanup', [\App\Http\Controllers\Admin\AdminLogController::class, 'cleanup'])->name('cleanup');
            Route::get('/statistics', [\App\Http\Controllers\Admin\AdminLogController::class, 'statistics'])->name('statistics');
        });

        // Debts routes
        Route::prefix('/debts')->name('admin.debts.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\DebtController::class, 'index'])->name('index');
            Route::post('/paginate', [\App\Http\Controllers\Admin\DebtController::class, 'paginate'])->name('paginate');
            Route::get('/export', [\App\Http\Controllers\Admin\DebtController::class, 'export'])->name('export');
            Route::get('/statistics', [\App\Http\Controllers\Admin\DebtController::class, 'statistics'])->name('statistics');
        });
    });

    Route::group(['middleware' => ['auth'], 'prefix' => 'user'], function () {
        Route::get('loadMore', [SMScontroller::class,'loadMore'])->name('general.sms.load.more');
        Route::controller(GoodsController::class)
            ->group(function () {
                Route::post('/parcels/find', 'findGoodById')->name('customer.parcels.find');
                Route::post('/declaration_update2', 'declaration_update2')->name('declaration.update2');
                Route::post('/courier', 'courier_user')->name('courier.update');
                Route::post('/courier_cancel', 'courier_user_cancel')->name('courier.cancel');
                Route::post('/parcels_new', "store_new_package")->name("parcels.store_new_package");
            });
        Route::controller(CustomerController::class)
            ->group(function () {
                Route::get('/packages', 'packages')->name('customer.redirect');
                Route::get('profile/personal', 'personal')->name('customer.personal');
                Route::get('profile/change_password', 'change_password')->name('customer.change_password');
                Route::post('profile/changePassword', 'changePassword')->name('customer.changePassword');
                Route::post('/update/{user}', 'profile_update')->name('personal.update');
                Route::get('/packages/{status?}', 'packages')->name('customer.packages');
                Route::get('/packages/invoice/{invoice}', 'invoice')->name('customer.invoice');
                //declaration
                Route::get('/packages/declaration/{good}', 'declaration')->name('customer.declaration');
                Route::get('profile/transactions', 'transactions')->name('customer.transactions');
                Route::get('profile/address', 'address')->name('customer.address');
            });
        //TBC
        Route::post('/tbcpay/topup', [UserController::class, 'topUpTbc'])->name("tbcpay.topup");
        //mars as read notificationsÒÒ
        Route::post('/mark-as-read', [SMScontroller::class, 'markNotification'])->name('markNotification');
    });

    Route::post('/user/tbcpay/success', [TbcPayController::class, 'success'])->name("tbcpay.success");
    Route::post('/user/tbcpay/fail', [TbcPayController::class, 'fail'])->name("tbcpay.fail");

    Route::post('/user/ipay/initiate', [IpayController::class, 'initiate'])->name("ipay.initiate");
    Route::get('/user/ipay/postback', [IpayController::class, 'postback'])->name("ipay.postback");
    Route::post('/user/ipay/postback', [IpayController::class, 'post'])->name("ipay.post");
    Route::post('/user/ipay/refund', [IpayController::class, 'refund'])->name("ipay.refund");


    Route::any("billing", [WebBillingTbcPayController::class, 'billing'])->middleware("billing.tbcpay");
    Route::any("billing/ipay", [WebBillingIpayController::class, 'billing'])->middleware("billing.ipay");


//Route::get('/fail', 'CustomerController@fail')->name('tbcpay.fail2');

//Route::prefix('service')->middleware('role:super')->group(function (){
    Route::prefix('service')->middleware('role:super')->group(function () {
        Route::get('get-users/{flightId}', [ServiceController::class, 'sendCustomerSms']);
    });


});


