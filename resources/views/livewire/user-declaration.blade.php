<div>
    <div class="container">
        <div class="card card-custom " id="kt_page_sticky_card">
            <div class="card-header">
                <div class="card-title">
                    <h3 class="card-label geo">
                        @lang('trans.declaration') <i class="mr-2"></i>
                        <small class="">{{$this->good->tracking_code}}</small>
                    </h3>
                    @if (session()->has('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif
                    @if (session()->has('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif
                </div>
                <div class="card-toolbar">
                    <a href="{{ url()->previous() }}" class="btn btn-light-primary font-weight-bolder mr-2">
                        <i class="ki ki-long-arrow-back icon-sm"></i>
                        @lang('trans.back')
                    </a>


                </div>
            </div>
            <div class="card-body">
                <!--begin::Form-->
                <form @if(!$good->flight?->declaration_lock) wire:submit.prevent="save" @endif>


                            <div class="form-group col-12 geo">
                                <label class="text-align: left">@lang('trans.message_type')</label>
                                <div class="radio-inline">
                                    <label class="radio" id="shop">
                                        <input wire:click="$set('show', true)" @if($good->flight?->declaration_lock) disabled @endif checked="checked" type="radio" name="parcel_type"/>
                                        <span></span>
                                        @lang('trans.online_shop')
                                    </label>
                                    <label class="radio" id="personal">
                                        <input wire:click="$set('show', false)"  @if($good->flight?->declaration_lock) disabled @endif type="radio" name="parcel_type"/>
                                        <span></span>
                                        @lang('trans.personal_message')
                                    </label>
                                </div>
                            </div>

                    <div class="form-group col-12  geo">
                        <label>@lang('trans.track_code')</label>
                        <input type="text"
                               class="form-control"
                               value="{{$this->good->tracking_code}}"
                        disabled />
                    </div>

                                <div class="form-group col-12  geo">
                                    <label>@lang('trans.branch')</label>
                                    <select wire:model.live="product.branch_id"
                                            @if($good->flight?->declaration_lock) disabled @endif
                                            class="form-control @error('product.branch_id') is-invalid @enderror" id="exampleSelect2">
                                        {{--                                        <option value="">-- აირჩიეთ ამანათის ფილიალი --</option>--}}
                                        @foreach ($branches as $branch)
                                            <option value="{{ $branch->id }}">{{$branch->title_en}}</option>
                                        @endforeach
                                    </select>
                                    @error('product.branch_id') <span class="geo text-danger error">{{ $message }}</span> @enderror
                                </div>
                            <div class="form-group col-12 geo" id="shop_name">
                                <label>@lang('trans.online_shop')</label>
                                <input wire:model="product.sender_company" @if($good->flight?->declaration_lock) disabled @endif type="text" name="sender_company" id="sender_company"
                                       class="form-control @error('product.sender_company') is-invalid @enderror"
                                       placeholder=""
                                       value=""/>
                                @error('product.sender_company') <span class="geo text-danger error">{{ $message }}</span> @enderror
                            </div>
                            <div class="form-group col-12 geo">
                                <div class="form-group">
                                    <label for="exampleSelect1">@lang('trans.parcel_code')</label>
                                    <select wire:model.live="product.category_id"
                                            @if($good->flight?->declaration_lock) disabled @endif
                                            class="form-control @error('product.category_id') is-invalid @enderror" id="exampleSelect1">
                                        <option value="">@lang('trans.parcel_category')</option>
                                        @foreach ($itemcategory as $itemcat)
                                            <option value="{{ $itemcat->id }}">{{$itemcat->code}} - {{$itemcat->description_ge}}</option>
                                        @endforeach
                                    </select>
                                    @error('product.category_id') <span class="geo text-danger error">{{ $message }}</span> @enderror
                                </div>
                            </div>
                            <div class="form-group col-12 geo">
                                <div class="form-group">
                                    <label>@lang('trans.parcel_price_gel')</label>
                                    <input wire:model.live="product.client_buy_amount" @if($good->flight?->declaration_lock) disabled @endif type="number" step="0.01"
                                           class="form-control @error('product.client_buy_amount') is-invalid @enderror"  name="client_buy_amount"
                                           id="client_buy_amount"
                                    />
                                    @error('product.client_buy_amount') <span class="geo text-danger error">{{ $message }}</span> @enderror
                                </div>
                                {{--                        <label class="text-danger">300 ლარზე მეტი ღირებულების ამანათისთვის შესყიდვის ინვოისის ატვირთვა სავალდებულოა</label>--}}
                                <div class="form-group">
                                    <p>@lang('trans.comment')</p>
                                    <textarea @if($good->flight?->declaration_lock) disabled @endif style="resize: none" id="userComment" wire:model.live="userComment" rows="3" cols="100" class="form-control" placeholder="მომხმარებლისთვის განკუთვნილი კომენტარის ველი"></textarea>

                                </div>
                                <div class="form-group">
                                    <label>@lang('trans.upload_file')</label>
                                    <input type="file" wire:model.live="cover_image" @if($good->flight?->declaration_lock) disabled @endif>
                                    {{--                                @error('cover_image') <span class="error">{{ $message }}</span> @enderror--}}
                                    @error('cover_image') <span class="geo text-danger error">{{ $message }}</span> @enderror
                                </div>
                            </div>
                            @if(!empty($this->good->cover_image))
                                <div class="form-group col-12 geo">
                                    <p>
                                    <span class="navi-icon">
                                        <i class="la la-print"></i>
                                    </span>
                                        <a id="cover_image3" href="{{ asset('/storage/invoice_images') . '/' . $this->good->cover_image}}" target="_blank"
                                           rel="noopener noreferrer">
                                            Invoice {{ $this->good->cover_image }}
                                        </a>

                                    </p>
                                </div>
                            @endif
                            <button type="submit" class="btn btn-primary"
{{--                                    @php--}}
{{--//dd($good->flight); @endphp--}}
                                    @if($good->flight?->declaration_lock)
                                        onclick="event.preventDefault(); alert('@lang('trans.declaration_locked_message')');"
{{--                                        disabled--}}
                                    @endif>
                                @lang('trans.save1')
                            </button>
                </form>

                <!--end::Form-->
            </div>
        </div>
    </div>

</div>