<div>
    <form wire:submit.prevent="save" class="w-full">
        <div class="flex flex-col items-start p-4">
            <div class="flex items-center w-full border-b pb-4">
                @if (session()->has('message'))
                    <div class="alert alert-success">
                        {{ session('message') }}
                    </div>
                @endif
            </div>
            <div class="form-group col-12 geo">
                <div class="form-group">
                    <label>ატვირთეთ სურათი</label>
                    <input type="file" wire:model.live="cover_image">
                </div>
            </div>
            <div class="form-group col-12 geo">
                <p>
                    <a id="cover_image3" href="{{ asset('/storage') . '/' . $this->sms_slider->image}}" target="_blank"
                       rel="noopener noreferrer">
{{--                        invoice {{ $this->cover_image }}--}}
                        {{$this->sms_slider->image}}

                    </a>
                </p>
            </div>
            <div class="ml-auto">
                <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                                                type="submit">{{'save'}}
                </button>

            </div>
        </div>
    </form>
</div>
