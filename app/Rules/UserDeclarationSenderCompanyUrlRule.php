<?php

namespace App\Rules;

use App\Livewire\Packages;
use Illuminate\Contracts\Validation\Rule;

class UserDeclarationSenderCompanyUrlRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if ($value == 'pinduoduo')
        {
            return true;
        }

        return str_contains($value, '.');
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'მიუთითეთ ვებ გვერდი სწორი ფორმატით, საიდანაც გამოიწერეთ ამანათი.';
    }
}
