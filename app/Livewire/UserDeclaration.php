<?php

namespace App\Livewire;

use App\Models\Branch;
use App\Models\goods;
use App\Models\ItemCategory;
use App\Models\UserBranchChangeLog;
use App\Rules\Goods\DeclarationCoverImageRule;
use App\Rules\UserDeclarationSenderCompanyUrlRule;
use App\Support\SMS;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithFileUploads;

class UserDeclaration extends Component
{
    use WithFileUploads;

//    public $showModal = false;
    public $show = true;
    public $productId;
    public $product;
    public $itemcategory;
    public $branches;
    public $cover_image;
    public $new_photo;
    public $good;
    public $userComment;


    protected $paginationTheme = 'bootstrap';

    protected function rules(): array
    {
        return [
            'product.tracking_code' => 'required',
            'product.sender_company' => 'required|regex:/^[a-zA-Z0-9\-\._~\:\/\?#\[\]@!\$&\'\(\)\*\+,;\=]*$/',
            'product.branch_id' => 'required',
            'product.rec_name' => 'required',
            'product.category_id' => 'required',
            'product.client_buy_amount' => 'required|numeric|not_in:0|regex:/^\d+\.\d+$/',
            'product.must_customize' => 'required',
            'cover_image' => [new DeclarationCoverImageRule],
            'userComment' => 'nullable|string'
            //'product.quantity' => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'product.tracking_code.required' => 'ტრექინგ კოდის შეყვანა აუცილებელია',
            'product.client_buy_amount.required' => 'ღირებულების შეყვანა აუცილებელია',
            'product.client_buy_amount.numeric' => 'მნიშვნელობა უნდა იყოს რიცხვითი',
            'product.client_buy_amount.not_in' => 'მნიშვნელობა უნდა იყოს ნულზე მეტი',
            'product.client_buy_amount.*' => 'ამანათის ღირებულება უნდა შეიტანოთ თეთრების სიზუსტით, მთელი რიცხვის მითითებას ვერ შეძლებთ!',
            'product.quantity' => 'ამანათის რაოდენობის მითითება სავალდებულოა',
            'cover_image.required_if' => 'ამანათი ექვემდებარება განბაჟებას 300 ლარზე მეტი ღირებულების ამანათისთვის შესყიდვის ინვოისის ატვირთვა სავალდებულოა',
            'product.category_id.required' => 'ამანათის კატეგორიის არჩევა სავალდებულოა',
        ];
    }

    public function mount()
    {
//        $this->showModal = false;
        $this->itemcategory = ItemCategory::all();
        $this->branches = Branch::all();
        $this->new_photo = $this->good->cover_image;
        $this->product = $this->good;
        $this->userComment = $this->good->user_declaration_comment;
    }

//    public function getGoodsProperty()
//    {
//        return Goods::find($this->productId);
//    }
    public function updatedCover_image()
    {
        $this->validate([
            'cover_image' => 'image|max:1999', // 1MB Max
        ]);
    }


    public function save()
    {
        //dd($this->userComment);
        if($this->good->flight->locked == 1)
        {
            return 'დეკლარაციის შევსება არ არის შესაძლებელი, რადგან ამანათის რეისზე დაგვიანებულია დეკლარაცია. მიმართეთ ადმინისტრაციას.';
        }

        // Check if declaration is locked
        if($this->good->flight?->declaration_lock == true)
        {
            session()->flash('error', __('trans.declaration_locked_message'));
            return redirect()->back();
        }
//        $this->product->must_customize = false;
//        dd($this->product->client_buy_amount);

        $this->validate();
//        dd($this->product->getOriginal('branch_id'), $this->product->branch_id);
        if ($this->product->branch_id != $this->product->getOriginal('branch_id'))
        {
            $branchTitleFrom = Branch::query()->find($this->product->getOriginal('branch_id'))->title_ge;
            $branchTitleTo = $this->product->branch->title_ge;

            $smsText = 'თქვენ შუეცვალეთ ამანათს: '.$this->product->tracking_code.', ფილიალი: '.$branchTitleTo;
            (new SMS($this->product->user->phone, $smsText))->send();

            UserBranchChangeLog::query()->create([
                'tracking_code' => $this->product->tracking_code,
                'user_id' => auth()->id(),
                'branch_id_from' => $this->product->getOriginal('branch_id'),
                'branch_id_to' => $this->product->branch_id,
            ]);
        }
        if (!is_null($this->good->id)) {
            // Calculate the total client_buy_amount for this user
            $totalClientBuyAmount = goods::where('user_id', Auth::user()->id)
                ->where('flight_id', $this->good->flight_id)
                ->where('id', '!=', $this->good->id)
                ->sum('client_buy_amount');
            $totalClientBuyAmount2 = $totalClientBuyAmount+$this->product->client_buy_amount;

            // Check if the sum of all products is over 300 or equals
//            get goods on this flight
            $declaretion_goods = goods::where('user_id', Auth::user()->id)
                ->where('flight_id', $this->good->flight_id)
                ->get();
            if ($totalClientBuyAmount2 >= 300) {
//                am reisze yvela amanats am pirovnebis daeweros gansabajebeli
                $declaretion_goods->each(function ($good) {
                    $good->update(['must_customize' => 1]);
                });
                $this->product->must_customize = 1;
            }else{
                $declaretion_goods->each(function ($good) {
                    $good->update(['must_customize' => 0]);
                });
                $this->product->must_customize = 0;
            }
            $this->product->is_declared = 1;
            $this->product->user_declaration_comment = $this->userComment;
            $this->product->save();
        }
        if (!empty($this->cover_image)) {
            //generate file
            //get filename
            $filenameWithExt = $this->cover_image->getClientOriginalName();
            //get just filename
            $filename = pathinfo($filenameWithExt, PATHINFO_FILENAME);
            //get just extensions
            $extension = $this->cover_image->getClientOriginalExtension();
            // Filename to store
            $fileNameToStore = $filename . '_' . time() . '.' . $extension;
            // Upload Image
            $path = $this->cover_image->storeAs('public/invoice_images', $fileNameToStore);

            $this->product->cover_image = $fileNameToStore;
            $this->product->is_declared = 1;
            $this->product->save();
        }
        session()->flash('success', 'დეკლარაცია წარმატებით განხორციელდა.');
        return redirect()->to(session('previous_page'));
    }


    public function render()
    {
        return view('livewire.user-declaration',
            [
//                'products' => Goods::latest()->paginate(5)
            ]);
    }
}
