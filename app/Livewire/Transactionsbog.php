<?php

namespace App\Livewire;

use App\Models\IpayTransaction;
use Mediconesystems\LivewireDatatables\BooleanColumn;
use Mediconesystems\LivewireDatatables\Column;
use Mediconesystems\LivewireDatatables\DateColumn;
use Mediconesystems\LivewireDatatables\Http\Livewire\LivewireDatatable;
use Mediconesystems\LivewireDatatables\NumberColumn;

class Transactionsbog extends LivewireDatatable
{
    protected $appends = ['Undeclared'];
    public $complex = true;
    public $exportable = true;
    public  $bulkDisabled = true;
    public  $GoodsStatus;


    public function builder()
    {
        //
        return IpayTransaction::query()
            ->leftJoin('users', 'users.id', 'ipay_transactions.user_id');
//            ->leftJoin('branches', 'branches.id', 'goods.branch_id');

    }

    public function columns()
    {
        //
        return [


//            Column::checkbox(),

            NumberColumn::name('id')
                ->label('ID')
                ->filterable(),
//                ->linkTo('admin/parcels/edit', 6),
            Column::name('users.user_room_code')
                ->label('ოთახის ნომერი')
                ->filterable()
                ->searchable(),

            Column::name('users.identification')
                ->label('საიდენტიფიკაციო')
                ->filterable()
                ->searchable(),
            Column::name('status')
                ->label('სტატუსი')
                ->filterable()
                ->searchable(),
            Column::name('payment_method')
                ->label('გადახდის მეთოდი')
                ->filterable()
                ->searchable(),
            Column::name('card_type')
                ->label('ბარათის ტიპი')
                ->filterable()
                ->searchable(),
            Column::name('amount')
                ->label('თანხა')
                ->filterable()
                ->searchable(),
            Column::name('order_id')
                ->label('order_id')
                ->filterable()
                ->searchable(),
            Column::name('shop_order_id')
                ->label('shop_order_id')
                ->filterable()
                ->searchable(),
            Column::name('payment_hash')
                ->label('payment_hash')
                ->filterable()
                ->searchable(),


        ];
    }
}
