<?php

namespace App\Livewire;

use App\Models\goods;
use App\Models\ItemCategory;
use Livewire\Component;
use Livewire\WithFileUploads;

class Declaration extends Component
{
    use WithFileUploads;

    public $showModal = false;
    public $show = true;
    public $productId;
    public $product;
    public $itemcategory;
    public $cover_image;
    public $new_photo;


    protected $paginationTheme = 'bootstrap';

    protected $rules = [
        'product.tracking_code' => 'required',
        'product.sender_company' => 'required',
//        'product.rec_name' => 'required',
        'product.category_id' => 'required',
        'product.client_buy_amount' => 'required',
        'cover_image' => 'required_if:product.must_customize,1',
        'product.must_customize' => 'required',
        'product.quantity' => 'required',
    ];

    protected $messages = [
        'product.tracking_code.required' => 'The tracking_code cannot be empty.',
        'product.sender_company.required' => 'The sender_company is not valid.',
        'product.quantity' => 'ამანათის რაოდენობის მითითება სავალდებულოა',
        'product.cover_image.required_if' => '300 ლარზე მეტი ღირებულების ამანათისთვის შესყიდვის ინვოისის ატვირთვა სავალდებულოა',
        'product.category_id' => 'ამანათის კატეგორიის არჩევა სავალდებულოა',
    ];


    public function mount()
    {
        $this->itemcategory = ItemCategory::all();

        $this->new_photo = $this->goods->cover_image;
    }

    public function getGoodsProperty()
    {
        return goods::find($this->productId);
    }
    public function updatedCover_image()
    {
        $this->validate([
            'cover_image' => 'image|max:1999', // 1MB Max
        ]);
    }


    public function render()
    {
        return view('livewire.declaration',
            [
                'products' => goods::latest()->paginate(5)
            ]);
    }
    //edit button sumoning modal
    public function edit($productId)
    {
        $this->showModal = true;
        $this->productId = $productId;
        $this->product = goods::find($productId);
//        dd($this->showModal);
    }


    public function save()
    {

        $this->validate();
        if (!is_null($this->productId)) {
            //tu girebuleba 300 acda mashin chaiweros avtomaturad ganbajeba
            if($this->product->client_buy_amount>=300){
                $this->product->must_customize = 1;
            }
            else
            {
                $this->product->must_customize = 0;
            }
            $this->product->is_declared = 1;
            $this->product->save();
        }
        if (!empty($this->cover_image)) {
            //generate file
            //get filename
            $filenameWithExt = $this->cover_image->getClientOriginalName();
            //get just filename
            $filename = pathinfo($filenameWithExt, PATHINFO_FILENAME);
            //get just extensions
            $extension = $this->cover_image->getClientOriginalExtension();
            // Filename to store
            $fileNameToStore = $filename . '_' . time() . '.' . $extension;
            // Upload Image
            $path = $this->cover_image->storeAs('public/invoice_images', $fileNameToStore);

            $this->product->cover_image = $fileNameToStore;
            $this->product->save();
        }


//        else {
//            $fileNameToStore ='noimage.jpg';
//        }

//        else {
//            Product::create($this->product);
//        }
        $this->showModal = false;
    }

    public function close()
    {
        $this->showModal = false;
    }

    public function delete($productId)
    {
        $product = goods::find($productId);
        if ($product) {
            $product->delete();
        }
    }


}
