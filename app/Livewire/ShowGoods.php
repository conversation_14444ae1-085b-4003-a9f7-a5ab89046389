<?php

namespace App\Livewire;

use App\Models\Branch;
use App\Models\Flight;
use App\Models\goods;
use App\Models\User;
use Illuminate\Support\Facades\Storage;
use Mediconesystems\LivewireDatatables\BooleanColumn;
use Mediconesystems\LivewireDatatables\Column;
use Mediconesystems\LivewireDatatables\DateColumn;
use Mediconesystems\LivewireDatatables\Http\Livewire\LivewireDatatable;
use Mediconesystems\LivewireDatatables\NumberColumn;
use Mediconesystems\LivewireDatatables\TimeColumn;

class ShowGoods extends LivewireDatatable
{
//    public $hideable = 'select';
    protected $appends = ['Undeclared'];
    public $complex = true;
    public $exportable = true;
    public  $bulkDisabled = true;
    public  $GoodsStatus;
//    public  $selectedCategory = null;
//    public $selected;
//    public $afterTableSlot = 'selected';


    public function builder()
    {
        return goods::query()
            ->leftJoin('flights', 'flights.id', 'goods.flight_id')
            ->leftJoin('branches', 'branches.id', 'goods.branch_id');

    }

    public function columns()
    {
        return [
            Column::checkbox(),

            NumberColumn::name('id')
                ->label('ID')
                ->filterable()
                ->linkTo('admin/parcels/edit', 6),

            Column::name('tracking_code')
                ->label('კოდი')
                ->filterable()
                ->searchable(),
            Column::name('rec_name')
                ->label('მომხმარებელი')
                ->filterable()
                ->searchable(),

            Column::name('room_number')
                ->filterable()
                ->label('ოთახის ნომერი')
                ->searchable(),

            Column::name('flights.flight_number')
                ->filterable()
                ->label('ფრენის ნომერი')
                ->searchable(),
            Column::name('small_comment')
                ->filterable(['SCANNED', 'NONE'])
                ->label('SCAN STATUS')
                ->searchable(),



            DateColumn::name('flights.takeout_date')
                ->label('ფრენის თარიღი')
                ->filterable(),

            NumberColumn::name('phisicalw')
                ->label('წონა')
                ->searchable(),
            NumberColumn::name('price_to_pay')
                ->label('თანხა')
                ->searchable(),
            Column::name('flight_parcel_state')
                ->label('ამანათის სტატუსი')
//                ->filterable()
                ->filterable(['RECIEVED', 'TOOK_OUT', 'SENT', 'WAITING','SUSPENDED', 'NONE'])
//                ->filterable($this->goods->pluck('flight_parcel_state'))
                ->searchable(),


            BooleanColumn::name('is_payed')
                ->label('გადახდა')
                ->filterable(),
            BooleanColumn::name('is_declared')
                ->label('დეკლარაცია')
                ->filterable(),
            BooleanColumn::name('must_customize')
                ->label('განბაჟება')
                ->filterable(),
            BooleanColumn::name('uses_courier')
                ->label('საკურიერო')
                ->filterable(),

            Column::name('branches.title_en')
                ->label('branch_id')
                ->filterable($this->branches)
//                ->filterable()
//                ->filterable($this->goods->pluck('flight_parcel_state'))
                ->searchable(),

//            Column::callback(['id', 'pid'], function ($id, $pid) {
//                return view('livewire.datatables.datatable', ['id' => $id, 'pid' => $pid]);
//            })

//            Column::callback(['id', 'pid'], function ($id, $pid) {
//                return view('livewire.datatables.table-action', ['id' => $id, 'pid' => $pid]);
//            })
//            Column::delete(),
            Column::callback(['id', 'pid'], function ($id, $pid) {

                return view('livewire.datatables.table-action',
                    [
                        'id' => $id,
                        'pid' => $pid
                    ]);

            })->excludeFromExport()

        ];
    }

//    public function getPlanetsProperty()
//    {
//        return Planet::pluck('name');
//    }

//
    public function getFlightsProperty()
    {
        return Flight::pluck('flight_parcel_state');
    }
    public function getBranchesProperty()
    {
        return Branch::pluck('title_en');
    }
    public function delete($id)
    {
        $this->deleteCommand($id);
    }

    public function hardDelete($id)
    {
        $this->deleteCommand($id, true);
    }

    public function deleteCommand($id, bool $hard=false): void
    {
        $product = goods::find($id);
        if ($product) {
//            suratis washla
            if($product->cover_image !='noimage.jpg'){
                Storage::delete('public/invoice_images/'.$product->cover_image);
            }
//user is balance i cvlileba
            $user = User:: where("id", $product->user_id)->first();
//            $flight = Flight:: where("id", $product->flight_id)->first();
            if ($product->flight_parcel_state == 'RECIEVED' || $product->flight_parcel_state == 'TOOK_OUT'){
                $user->balance = $user->balance + $product->price_to_pay;
                $user->save();
            }
            if ($hard)
            {
                $product->forceDelete();
            }else{
                $product->delete();
            }
        }
    }


//
//    public function getWeaponsProperty()
//    {
//        return Weapon::all();
//    }

//    public function computeBedtime($date)
//    {
//        if (!$date) {
//            return;
//        }
//        return Carbon::parse($date)->isPast()
//            ? Carbon::parse($date)->addDay()->diffForHumans(['parts' => 2])
//            : Carbon::parse($date)->diffForHumans(['parts' => 2]);
//    }
}
