<?php

namespace App\Livewire;

use App\Mail\RegistrationMail;
use App\Models\Branch;
use App\Models\city;
use App\Models\User;
use App\Support\SMS;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Livewire\Component;

class NewRegistration extends Component
{
    use AuthorizesRequests;
//    public $name;
    public $show = false;
    public $cities;
    public $branches;
    //passed variables top
    public $username;
    public $password;
    public $password_confirmation;
    public $first_name_ge;
    public $last_name_ge;
    public $first_name_en;
    public $last_name_en;
    public $identification;
    public $company_identification;
    public $company_name;
    public $phone;
    public $address_1;
    public $city_id=1;
    public $branch_id=1;
    public $is_foreigner;
    public $checkbox1;
    public $partner_status;

//    public $redirectTo = RouteServiceProvider::HOME;

//    public function rules()
//    {
//        return [
//            'username' => ['required', 'unique:users'],
//            'first_name_ge' => ['required', 'integer', 'min:10'],
//            'last_name_ge' => ['required', 'min:3'],
//            'company_identification' => ['required', 'email:rfc,dns', 'unique:users', new company_identification()],
//            'password' => ['required', 'min:8', 'same:passwordConfirmation'],
//        ];
//
//    }

    protected $rules = [
        'username' => 'required|string|email|max:255|unique:users',
        'password' => 'required|string|min:4|same:password_confirmation',
        'first_name_ge' => 'required|string|max:255',
        'last_name_ge' => 'required|string|max:255',
        'first_name_en' => 'required|string|max:255',
        'last_name_en' => 'required|string|max:255',
        'identification' => 'required|numeric|unique:users|digits_between:9,11',
//        'identification' => 'required_without:company_identification|numeric|digits_between:11,11|unique:users',
//        'company_identification' => 'required_without:identification|numeric',
        'company_name' => 'required_if:show,true',
        'phone' => 'required|numeric|digits_between:9,9',
        'address_1' => 'required|string|max:255',
        'city_id' => 'required|integer',
        'branch_id' => 'required|integer',
        'checkbox1' => 'accepted',

    ];



    protected $messages = [
        'username.required' => 'მომხმარებლის სახელის შეყვანა აუცილებელია',
        'username.unique' => 'ასეთი ელ.ფოსტა უკვე არსებობს',
        'password.required' => 'პაროლის შეყვანა აუცილებელია',
        'password.confirmation' => 'განმეორებით შეტანილი პაროლი არ ემთხვევა.',
        'password.confirmed' => 'პაროლი არ ემთხვევა დადასტურებას.',
        'first_name_en.required' => 'სახელის შეყვანა ლათინურად აუცილებელია!',
        'last_name_en.required' => 'გვარის შეყვანა ლათინურად აუცილებელია',
        'first_name_ge.required' => 'სახელის შეყვანა აუცილებელია',
        'last_name_ge.required' => 'გვარის შეყვანა აუცილებელია',
        'identification.required' => 'პირადი ნომრის/საიდენტიფიკაციოს შეყვანა აუცილებელია',
        'identification.numeric' => 'პირადი ნომერი/საიდენტიფიკაციო უნდა იყოს რიცხვითი',
        'identification.string' => 'პირადი ნომერი/საიდენტიფიკაციო შეყვანა აუცილებელია',
        'identification.digits_between' => 'პირადი ნომერი/საიდენტიფიკაციო უნდა შედგებოდეს 11/9 რიცხვისგან',
        'company_identification.required' => 'საიდენტიფიკაციოს შეყვანა აუცილებელია',
        'phone.required' => 'მობილურის ნომერის შეყვანა აუცილებელია',
        'phone.max' => 'მობილურის ნომერი არ უნდა აღემატებოდეს 9 სიმბოლოს.',
        'phone.numeric' => 'მობილურის ნომერი უნდა იყოს რიცხვი.',
        'address_1.required' => 'მისამართის შეყვანა აუცილებელია',
        'address_2.required' => 'მისამართის შეყვანა აუცილებელია',
        'city_id.required' => 'ქალაქის არჩევა აუცილებელია',
        'branch_id.required' => 'ფილიალის არჩევა აუცილებელია',
        'checkbox1.accepted' => 'აუცილებელია დაეთანხმოთ წესებს და პირობებს',
        'company_name.required_if' => 'აუცილებელია კომპანიის სახელი',
    ];
    public function mount()
    {
        //
        $this->cities  = city::orderBy("id", "asc")->get();
        $this->branches = Branch::orderBy("id", "asc")->get();

    }

//    live validating
//    public function updated($propertyName)
//    {
//        $this->validateOnly($propertyName);
//    }

    public function submit()
    {
        if ($this->is_foreigner)
        {
            $this->rules['identification'] = 'sometimes|string|unique:users';
        }
        $this->validate();

        $user = User::create([
            'username' => $this->username,
            'email' => $this->username,
            'password' => Hash::make($this->password),
            'first_name_en' => $this->first_name_en,
            'last_name_en' => $this->last_name_en,
            'first_name_ge' => $this->first_name_ge,
            'last_name_ge' => $this->last_name_ge,
            'identification' => $this->identification,
            'company_name' => $this->company_name,
            'phone' => $this->phone,
            'address_1' => $this->address_1,
            'city_id' => $this->city_id,
            'branch_id' => $this->branch_id,
            'checkbox1' => $this->checkbox1,
            'user_room_code' => 1,
            'role_id' => 1,
            'user_wants_sms' => 1,
            'user_wants_email' => 1,
            'is_active' => 1,
            'is_foreigner' => $this->is_foreigner,
        ]);

        if(!is_null($user->company_name)) {
            $user->is_juridical=1;
            $user->save();
        }


        if ($this->partner_status =='KVIKVO') {
            // otaxis nomris generacia snx room code da snh frenis

            $silver = 'SNXK';

            $new = str_pad($user->id, 5, 0, STR_PAD_LEFT);
            $new_room_code = $silver.''.$new;

            $user_check = User::where('user_room_code', '=', $new_room_code)->first();
            $id=$user->id;
            if($user_check !== null) {
                do {
                    if($user_check !== null) {
                        $incremented_room_code = $silver.''.str_pad($id, 5, 0, STR_PAD_LEFT);
                        $id++;
                    }
                }
                while ($user_check = User::where('user_room_code', '=', $incremented_room_code)->first());
                $user->user_room_code = $incremented_room_code;
                $user->save();
            } else {
                $user->user_room_code = $new_room_code;
                $user->save();
            }
        }

        elseif($this->partner_status =='2SHOP') {
            $silver = 'SNXT';

            //increment the number by 1 and pad with 0 in left.
            //                davagenerire axali id

            $new = str_pad($user->id, 5, 0, STR_PAD_LEFT);
            $new_room_code = $silver.''.$new;

            $user_check = User::where('user_room_code', '=', $new_room_code)->first();
            $id=$user->id;
            if($user_check !== null) {
                do {
                    if($user_check !== null) {
                        $incremented_room_code = $silver.''.str_pad($id, 5, 0, STR_PAD_LEFT);
                        $id++;
                    }
                }
                while ($user_check = User::where('user_room_code', '=', $incremented_room_code)->first());
                $user->user_room_code = $incremented_room_code;
                $user->save();
            } else {
                $user->user_room_code = $new_room_code;
                $user->save();
            }
        }
        else{

            $silver = 'SNX';

            //increment the number by 1 and pad with 0 in left.
            //                davagenerire axali id

            $new = str_pad($user->id, 5, 0, STR_PAD_LEFT);
            $new_room_code = $silver.''.$new;

            $user_check = User::where('user_room_code', '=', $new_room_code)->first();
            $id=$user->id;
            if($user_check !== null) {
                do {
                    if($user_check !== null) {
                        $incremented_room_code = $silver.''.str_pad($id, 5, 0, STR_PAD_LEFT);
                        $id++;
                    }
                }
                while ($user_check = User::where('user_room_code', '=', $incremented_room_code)->first());
                $user->user_room_code = $incremented_room_code;
                $user->save();
            } else {
                $user->user_room_code = $new_room_code;
                $user->save();
            }
        }

        Mail::to($user->email)->send(new RegistrationMail());

        $userRoomNumber = $new_room_code;
        if($user->phone)
        {
            $smsText = "მოგესალმებით, თქვენ წარმატებით გაიარეთ რეგისტრაცია, თქვენი ოთახის ნომერია: $userRoomNumber. მადლობა, რომ აირჩიეთ სანექსი!";
            (new SMS($user->phone, $smsText))->send();
        }

        if (Auth::attempt(['username' => $this->username, 'password' => $this->password])) {
            // Authentication was successful...
//            request()->session()->regenerate();
            session()->flash('message', "You are Login successful.");
            return redirect(route('customer.redirect'));
//            return redirect()->to('/user/packages');
        }

    }

    public function render()
    {
        return view('livewire.new-registration');
//        return view('livewire.show-posts', [
//            'posts' => Post::all(),
//        ]);
    }

}
