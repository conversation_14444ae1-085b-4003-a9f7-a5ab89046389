<?php

namespace App\Livewire;

use App\Models\SMSslider;
use Livewire\Component;
use Livewire\WithFileUploads;

class CoverPhoto extends Component
{
    use WithFileUploads;

    public $goods;
    public $cover_image;
    public $new_photo;
    public $sms_slider;

    protected $rules = [
        'cover_image' => 'required|image|max:10024',

    ];
    public function mount()
    {
        $this->sms_slider = SMSslider::first();

//        $this->new_photo = $this->goods->cover_image;
    }


    public function save()
    {

        $this->validate();

        if (!empty($this->cover_image)) {
            //generate file
            //get filename
            $filenameWithExt = $this->cover_image->getClientOriginalName();
            //get just filename
            $filename = pathinfo($filenameWithExt, PATHINFO_FILENAME);
            //get just extensions
            $extension = $this->cover_image->getClientOriginalExtension();
            // Filename to store
            $fileNameToStore = $filename . '_' . time() . '.' . $extension;
            // Upload Image
            $path = $this->cover_image->storeAs('public', $fileNameToStore);
//            $path = $this->cover_image->storeAs('public/invoice_images', $fileNameToStore);

            $this->sms_slider->image = $fileNameToStore;
            $this->sms_slider->save();
        }
        session()->flash('message', 'Cover successfully uploaded.');



//        $this->showModal = false;
    }


    public function render()
    {
        return view('livewire.cover-photo',
            [
                'sms_slider' => SMSslider::latest()->paginate(5)
            ]);
    }
}
