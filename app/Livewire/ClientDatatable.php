<?php

namespace App\Livewire;

use App\Models\Branch;
use App\Models\User;
use Mediconesystems\LivewireDatatables\BooleanColumn;
use Mediconesystems\LivewireDatatables\Column;
use Mediconesystems\LivewireDatatables\DateColumn;
use Mediconesystems\LivewireDatatables\Http\Livewire\LivewireDatatable;
use Mediconesystems\LivewireDatatables\NumberColumn;

class ClientDatatable extends LivewireDatatable
{
    protected $appends = ['Undeclared'];

    public function builder()
    {
        //
        return User::query()
            ->leftJoin('goods', 'goods.id', 'users.id')
            ->leftJoin('branches', 'branches.id', 'users.branch_id')
            ->orderBy('users.id', 'desc');
    }

    public function columns()
    {
        //
        return [
//            Column::checkbox(),
            NumberColumn::name('id')
                ->label('ID')
                ->filterable()
                ->linkTo('admin/user/edit', 6),


            Column::name('user_room_code')
                ->label('user_room_code')
                ->filterable()
                ->searchable(),
            Column::name('username')
                ->label('username')
                ->filterable()
                ->searchable(),
            Column::name('first_name_ge')
                ->label('first_name_ge')
                ->filterable()
                ->searchable(),
            Column::name('last_name_ge')
                ->label('last_name_ge')
                ->filterable()
                ->searchable(),
            Column::name('identification')
                ->label('identification')
                ->filterable()
                ->searchable(),
            Column::name('phone')
                ->label('phone')
                ->filterable()
                ->searchable(),
            NumberColumn::name('balance')
                ->label('balance')
                ->filterable()
                ->searchable(),
            Column::name('branches.title_en')
                ->label('branch_id')
                ->filterable($this->branches)
                ->searchable(),
            Column::name('created_at')
                ->label('created_at')
                ->filterable($this->branches)
                ->searchable(),

//            NumberColumn::name('undeclared')
//                ->label('undeclared')
//                ->filterable()
//                ->searchable(),

        ];
    }


    public function getBranchesProperty()
    {
        return Branch::pluck('title_en');
    }
}
