{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "consoletvs/charts": "^6.6", "endroid/qr-code": "^5.0", "guzzlehttp/guzzle": "^7.2", "josiasmontag/laravel-recaptchav3": "^1.0", "laravel/framework": "^12.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.7", "laravel/ui": "^4.0", "livewire/livewire": "^3.0", "maatwebsite/excel": "^3.1", "mailchimp/marketing": "^3.0", "mcamara/laravel-localization": "^2.3", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/laravel-ignition": "^2.0", "spatie/laravel-permission": "^6.0", "spatie/laravel-translatable": "^6.9"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}